import AsyncStorage from '@react-native-async-storage/async-storage';

// Keys for storage
const STORAGE_KEYS = {
  DAILY_CHECKINS: 'daily_checkins',
  HABITS: 'habits',
  HABIT_COMPLETIONS: 'habit_completions',
};

// Daily Check-in functions
export const saveDailyCheckin = async (checkinData) => {
  try {
    const existingCheckins = await getDailyCheckins();
    const today = new Date().toDateString();
    
    const updatedCheckins = {
      ...existingCheckins,
      [today]: checkinData,
    };
    
    await AsyncStorage.setItem(
      STORAGE_KEYS.DAILY_CHECKINS,
      JSON.stringify(updatedCheckins)
    );
    return true;
  } catch (error) {
    console.error('Error saving daily check-in:', error);
    return false;
  }
};

export const getDailyCheckins = async () => {
  try {
    const checkins = await AsyncStorage.getItem(STORAGE_KEYS.DAILY_CHECKINS);
    return checkins ? JSON.parse(checkins) : {};
  } catch (error) {
    console.error('Error getting daily check-ins:', error);
    return {};
  }
};

export const getTodaysCheckin = async () => {
  try {
    const checkins = await getDailyCheckins();
    const today = new Date().toDateString();
    return checkins[today] || null;
  } catch (error) {
    console.error('Error getting today\'s check-in:', error);
    return null;
  }
};

// Habits functions
export const saveHabit = async (habit) => {
  try {
    const existingHabits = await getHabits();
    const newHabit = {
      id: Date.now().toString(),
      ...habit,
      createdAt: new Date().toISOString(),
    };
    
    const updatedHabits = [...existingHabits, newHabit];
    await AsyncStorage.setItem(STORAGE_KEYS.HABITS, JSON.stringify(updatedHabits));
    return newHabit;
  } catch (error) {
    console.error('Error saving habit:', error);
    return null;
  }
};

export const getHabits = async () => {
  try {
    const habits = await AsyncStorage.getItem(STORAGE_KEYS.HABITS);
    return habits ? JSON.parse(habits) : [];
  } catch (error) {
    console.error('Error getting habits:', error);
    return [];
  }
};

export const deleteHabit = async (habitId) => {
  try {
    const existingHabits = await getHabits();
    const updatedHabits = existingHabits.filter(habit => habit.id !== habitId);
    await AsyncStorage.setItem(STORAGE_KEYS.HABITS, JSON.stringify(updatedHabits));
    return true;
  } catch (error) {
    console.error('Error deleting habit:', error);
    return false;
  }
};

// Habit completion functions
export const markHabitComplete = async (habitId, date = new Date().toDateString()) => {
  try {
    const completions = await getHabitCompletions();
    const dateKey = date;
    
    if (!completions[dateKey]) {
      completions[dateKey] = [];
    }
    
    if (!completions[dateKey].includes(habitId)) {
      completions[dateKey].push(habitId);
      await AsyncStorage.setItem(
        STORAGE_KEYS.HABIT_COMPLETIONS,
        JSON.stringify(completions)
      );
    }
    return true;
  } catch (error) {
    console.error('Error marking habit complete:', error);
    return false;
  }
};

export const unmarkHabitComplete = async (habitId, date = new Date().toDateString()) => {
  try {
    const completions = await getHabitCompletions();
    const dateKey = date;
    
    if (completions[dateKey]) {
      completions[dateKey] = completions[dateKey].filter(id => id !== habitId);
      await AsyncStorage.setItem(
        STORAGE_KEYS.HABIT_COMPLETIONS,
        JSON.stringify(completions)
      );
    }
    return true;
  } catch (error) {
    console.error('Error unmarking habit complete:', error);
    return false;
  }
};

export const getHabitCompletions = async () => {
  try {
    const completions = await AsyncStorage.getItem(STORAGE_KEYS.HABIT_COMPLETIONS);
    return completions ? JSON.parse(completions) : {};
  } catch (error) {
    console.error('Error getting habit completions:', error);
    return {};
  }
};

export const isHabitCompletedToday = async (habitId) => {
  try {
    const completions = await getHabitCompletions();
    const today = new Date().toDateString();
    return completions[today] ? completions[today].includes(habitId) : false;
  } catch (error) {
    console.error('Error checking habit completion:', error);
    return false;
  }
};
