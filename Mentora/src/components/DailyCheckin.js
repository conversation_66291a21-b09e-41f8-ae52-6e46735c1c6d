import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Slider,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { commonStyles, moodStyles, colors } from '../styles/styles';
import { saveDailyCheckin, getTodaysCheckin } from '../utils/storage';

const MOODS = [
  { emoji: '😢', label: 'Sad', value: 1 },
  { emoji: '😕', label: 'Down', value: 2 },
  { emoji: '😐', label: 'Okay', value: 3 },
  { emoji: '😊', label: 'Good', value: 4 },
  { emoji: '😄', label: 'Great', value: 5 },
];

const DailyCheckin = () => {
  const [selectedMood, setSelectedMood] = useState(null);
  const [energyLevel, setEnergyLevel] = useState(5);
  const [hasCheckedInToday, setHasCheckedInToday] = useState(false);
  const [todaysCheckin, setTodaysCheckin] = useState(null);

  useEffect(() => {
    loadTodaysCheckin();
  }, []);

  const loadTodaysCheckin = async () => {
    const checkin = await getTodaysCheckin();
    if (checkin) {
      setHasCheckedInToday(true);
      setTodaysCheckin(checkin);
      setSelectedMood(checkin.mood);
      setEnergyLevel(checkin.energy);
    }
  };

  const handleSubmitCheckin = async () => {
    if (selectedMood === null) {
      Alert.alert('Missing Information', 'Please select your mood before submitting.');
      return;
    }

    const checkinData = {
      mood: selectedMood,
      energy: energyLevel,
      timestamp: new Date().toISOString(),
    };

    const success = await saveDailyCheckin(checkinData);
    
    if (success) {
      setHasCheckedInToday(true);
      setTodaysCheckin(checkinData);
      Alert.alert('Success', 'Your daily check-in has been saved!');
    } else {
      Alert.alert('Error', 'Failed to save your check-in. Please try again.');
    }
  };

  const handleNewCheckin = () => {
    setHasCheckedInToday(false);
    setTodaysCheckin(null);
    setSelectedMood(null);
    setEnergyLevel(5);
  };

  const getMoodEmoji = (moodValue) => {
    const mood = MOODS.find(m => m.value === moodValue);
    return mood ? mood.emoji : '😐';
  };

  const getMoodLabel = (moodValue) => {
    const mood = MOODS.find(m => m.value === moodValue);
    return mood ? mood.label : 'Okay';
  };

  const getEnergyColor = (energy) => {
    if (energy <= 3) return colors.error;
    if (energy <= 6) return colors.warning;
    return colors.success;
  };

  if (hasCheckedInToday && todaysCheckin) {
    return (
      <SafeAreaView style={commonStyles.safeArea}>
        <View style={commonStyles.header}>
          <Text style={commonStyles.headerTitle}>Daily Check-in</Text>
        </View>
        
        <ScrollView style={commonStyles.content}>
          <View style={commonStyles.card}>
            <Text style={commonStyles.title}>✅ Check-in Complete!</Text>
            <Text style={commonStyles.subtitle}>
              Here's how you're feeling today:
            </Text>
            
            <View style={{ alignItems: 'center', marginVertical: 20 }}>
              <Text style={{ fontSize: 64, marginBottom: 8 }}>
                {getMoodEmoji(todaysCheckin.mood)}
              </Text>
              <Text style={[commonStyles.title, { marginBottom: 8 }]}>
                {getMoodLabel(todaysCheckin.mood)}
              </Text>
              
              <View style={{ alignItems: 'center', marginTop: 16 }}>
                <Text style={commonStyles.label}>Energy Level</Text>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
                  <Text style={{ fontSize: 24, marginRight: 12 }}>⚡</Text>
                  <Text style={[
                    { fontSize: 24, fontWeight: 'bold' },
                    { color: getEnergyColor(todaysCheckin.energy) }
                  ]}>
                    {todaysCheckin.energy}/10
                  </Text>
                </View>
              </View>
            </View>
            
            <TouchableOpacity
              style={commonStyles.secondaryButton}
              onPress={handleNewCheckin}
            >
              <Text style={commonStyles.secondaryButtonText}>
                Update Check-in
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <View style={commonStyles.header}>
        <Text style={commonStyles.headerTitle}>Daily Check-in</Text>
      </View>
      
      <ScrollView style={commonStyles.content}>
        <View style={commonStyles.card}>
          <Text style={commonStyles.title}>How are you feeling today?</Text>
          
          <Text style={commonStyles.label}>Mood</Text>
          <View style={moodStyles.moodContainer}>
            {MOODS.map((mood) => (
              <TouchableOpacity
                key={mood.value}
                style={[
                  moodStyles.moodButton,
                  selectedMood === mood.value && moodStyles.selectedMood,
                ]}
                onPress={() => setSelectedMood(mood.value)}
              >
                <Text style={moodStyles.moodEmoji}>{mood.emoji}</Text>
                <Text style={moodStyles.moodLabel}>{mood.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
          
          <View style={{ marginTop: 24 }}>
            <Text style={commonStyles.label}>Energy Level: {energyLevel}/10</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 12 }}>
              <Text style={{ marginRight: 12 }}>⚡</Text>
              <Slider
                style={{ flex: 1, height: 40 }}
                minimumValue={1}
                maximumValue={10}
                step={1}
                value={energyLevel}
                onValueChange={setEnergyLevel}
                minimumTrackTintColor={getEnergyColor(energyLevel)}
                maximumTrackTintColor={colors.border}
                thumbStyle={{ backgroundColor: getEnergyColor(energyLevel) }}
              />
              <Text style={{ marginLeft: 12, fontWeight: 'bold', color: getEnergyColor(energyLevel) }}>
                {energyLevel}
              </Text>
            </View>
          </View>
          
          <TouchableOpacity
            style={[
              commonStyles.button,
              { marginTop: 32, opacity: selectedMood ? 1 : 0.6 }
            ]}
            onPress={handleSubmitCheckin}
            disabled={!selectedMood}
          >
            <Text style={commonStyles.buttonText}>Submit Check-in</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default DailyCheckin;
