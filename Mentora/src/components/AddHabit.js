import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { commonStyles, colors } from '../styles/styles';
import { saveHabit } from '../utils/storage';

const HABIT_CATEGORIES = [
  { id: 'health', name: 'Health & Fitness', emoji: '💪' },
  { id: 'mindfulness', name: 'Mindfulness', emoji: '🧘' },
  { id: 'productivity', name: 'Productivity', emoji: '📈' },
  { id: 'learning', name: 'Learning', emoji: '📚' },
  { id: 'social', name: 'Social', emoji: '👥' },
  { id: 'creativity', name: 'Creativity', emoji: '🎨' },
  { id: 'other', name: 'Other', emoji: '⭐' },
];

const SUGGESTED_HABITS = {
  health: [
    'Drink 8 glasses of water',
    'Exercise for 30 minutes',
    'Take a 10-minute walk',
    'Get 8 hours of sleep',
    'Eat a healthy breakfast',
  ],
  mindfulness: [
    'Meditate for 10 minutes',
    'Practice gratitude',
    'Deep breathing exercises',
    'Journal for 5 minutes',
    'Practice mindful eating',
  ],
  productivity: [
    'Make my bed',
    'Plan tomorrow today',
    'Clean workspace',
    'Review daily goals',
    'Limit social media to 30 minutes',
  ],
  learning: [
    'Read for 20 minutes',
    'Learn a new word',
    'Practice a skill',
    'Watch educational content',
    'Take an online course',
  ],
  social: [
    'Call a friend or family member',
    'Send a thoughtful message',
    'Practice active listening',
    'Express appreciation',
    'Meet someone new',
  ],
  creativity: [
    'Write in a journal',
    'Draw or sketch',
    'Play a musical instrument',
    'Take photos',
    'Try a new recipe',
  ],
};

const AddHabit = ({ onClose, onHabitAdded }) => {
  const [habitName, setHabitName] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleSaveHabit = async () => {
    if (!habitName.trim()) {
      Alert.alert('Missing Information', 'Please enter a habit name.');
      return;
    }

    if (!selectedCategory) {
      Alert.alert('Missing Information', 'Please select a category.');
      return;
    }

    setSaving(true);
    
    try {
      const habitData = {
        name: habitName.trim(),
        category: selectedCategory,
      };

      const newHabit = await saveHabit(habitData);
      
      if (newHabit) {
        Alert.alert('Success', 'Habit added successfully!', [
          { text: 'OK', onPress: onHabitAdded }
        ]);
      } else {
        Alert.alert('Error', 'Failed to save habit. Please try again.');
      }
    } catch (error) {
      console.error('Error saving habit:', error);
      Alert.alert('Error', 'Failed to save habit. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSuggestionPress = (suggestion) => {
    setHabitName(suggestion);
    setShowSuggestions(false);
  };

  const getCategoryEmoji = (categoryId) => {
    const category = HABIT_CATEGORIES.find(cat => cat.id === categoryId);
    return category ? category.emoji : '⭐';
  };

  const getSuggestionsForCategory = () => {
    return selectedCategory ? SUGGESTED_HABITS[selectedCategory] || [] : [];
  };

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <KeyboardAvoidingView 
        style={{ flex: 1 }} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={commonStyles.header}>
          <View style={commonStyles.row}>
            <TouchableOpacity onPress={onClose}>
              <Text style={[commonStyles.buttonText, { color: colors.primary }]}>
                Cancel
              </Text>
            </TouchableOpacity>
            <Text style={commonStyles.headerTitle}>Add New Habit</Text>
            <TouchableOpacity 
              onPress={handleSaveHabit}
              disabled={saving}
            >
              <Text style={[
                commonStyles.buttonText, 
                { color: colors.primary, opacity: saving ? 0.6 : 1 }
              ]}>
                {saving ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <ScrollView style={commonStyles.content}>
          <View style={commonStyles.card}>
            <Text style={commonStyles.label}>Habit Name</Text>
            <TextInput
              style={commonStyles.input}
              value={habitName}
              onChangeText={setHabitName}
              placeholder="Enter your habit (e.g., Drink 8 glasses of water)"
              multiline
              maxLength={100}
            />
            
            {selectedCategory && (
              <TouchableOpacity
                style={commonStyles.secondaryButton}
                onPress={() => setShowSuggestions(!showSuggestions)}
              >
                <Text style={commonStyles.secondaryButtonText}>
                  {showSuggestions ? 'Hide' : 'Show'} Suggestions
                </Text>
              </TouchableOpacity>
            )}
            
            {showSuggestions && (
              <View style={{ marginTop: 16 }}>
                <Text style={commonStyles.subtitle}>Suggested habits:</Text>
                {getSuggestionsForCategory().map((suggestion, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      commonStyles.input,
                      { 
                        backgroundColor: colors.muted,
                        marginVertical: 4,
                        paddingVertical: 8,
                      }
                    ]}
                    onPress={() => handleSuggestionPress(suggestion)}
                  >
                    <Text style={{ color: colors.text }}>{suggestion}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          <View style={commonStyles.card}>
            <Text style={commonStyles.label}>Category</Text>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginTop: 8 }}>
              {HABIT_CATEGORIES.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    {
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 8,
                      paddingHorizontal: 12,
                      borderRadius: 20,
                      borderWidth: 1,
                      borderColor: colors.border,
                      margin: 4,
                      backgroundColor: colors.surface,
                    },
                    selectedCategory === category.id && {
                      backgroundColor: colors.primary + '20',
                      borderColor: colors.primary,
                    },
                  ]}
                  onPress={() => {
                    setSelectedCategory(category.id);
                    setShowSuggestions(false);
                  }}
                >
                  <Text style={{ fontSize: 16, marginRight: 6 }}>
                    {category.emoji}
                  </Text>
                  <Text style={[
                    { color: colors.text, fontSize: 14 },
                    selectedCategory === category.id && { color: colors.primary, fontWeight: '600' }
                  ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default AddHabit;
