import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { commonStyles, habitStyles, colors } from '../styles/styles';
import {
  getHabits,
  deleteHabit,
  markHabitComplete,
  unmarkHabitComplete,
  isHabitCompletedToday,
} from '../utils/storage';
import AddHabit from './AddHabit';

const MyHabits = () => {
  const [habits, setHabits] = useState([]);
  const [habitCompletions, setHabitCompletions] = useState({});
  const [showAddHabit, setShowAddHabit] = useState(false);
  const [loading, setLoading] = useState(true);

  useFocusEffect(
    useCallback(() => {
      loadHabits();
    }, [])
  );

  const loadHabits = async () => {
    try {
      setLoading(true);
      const habitsData = await getHabits();
      setHabits(habitsData);
      
      // Load completion status for each habit
      const completions = {};
      for (const habit of habitsData) {
        completions[habit.id] = await isHabitCompletedToday(habit.id);
      }
      setHabitCompletions(completions);
    } catch (error) {
      console.error('Error loading habits:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleHabit = async (habitId) => {
    const isCompleted = habitCompletions[habitId];
    
    try {
      if (isCompleted) {
        await unmarkHabitComplete(habitId);
      } else {
        await markHabitComplete(habitId);
      }
      
      setHabitCompletions(prev => ({
        ...prev,
        [habitId]: !isCompleted,
      }));
    } catch (error) {
      console.error('Error toggling habit:', error);
      Alert.alert('Error', 'Failed to update habit. Please try again.');
    }
  };

  const handleDeleteHabit = (habit) => {
    Alert.alert(
      'Delete Habit',
      `Are you sure you want to delete "${habit.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteHabit(habit.id);
            if (success) {
              loadHabits();
            } else {
              Alert.alert('Error', 'Failed to delete habit. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleAddHabit = () => {
    setShowAddHabit(true);
  };

  const handleHabitAdded = () => {
    setShowAddHabit(false);
    loadHabits();
  };

  const renderHabitItem = ({ item: habit }) => {
    const isCompleted = habitCompletions[habit.id] || false;
    
    return (
      <TouchableOpacity
        style={[
          habitStyles.habitItem,
          isCompleted && habitStyles.completedHabit,
        ]}
        onPress={() => handleToggleHabit(habit.id)}
        onLongPress={() => handleDeleteHabit(habit)}
      >
        <Text
          style={[
            habitStyles.habitText,
            isCompleted && habitStyles.completedHabitText,
          ]}
        >
          {habit.name}
        </Text>
        
        <TouchableOpacity
          style={[
            habitStyles.checkbox,
            isCompleted && habitStyles.checkedBox,
          ]}
          onPress={() => handleToggleHabit(habit.id)}
        >
          {isCompleted && (
            <Text style={habitStyles.checkmark}>✓</Text>
          )}
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  const getCompletionStats = () => {
    const totalHabits = habits.length;
    const completedHabits = Object.values(habitCompletions).filter(Boolean).length;
    return { totalHabits, completedHabits };
  };

  const { totalHabits, completedHabits } = getCompletionStats();
  const completionPercentage = totalHabits > 0 ? Math.round((completedHabits / totalHabits) * 100) : 0;

  if (showAddHabit) {
    return (
      <AddHabit
        onClose={() => setShowAddHabit(false)}
        onHabitAdded={handleHabitAdded}
      />
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <View style={commonStyles.header}>
        <Text style={commonStyles.headerTitle}>My Habits</Text>
      </View>
      
      <View style={commonStyles.content}>
        {/* Progress Card */}
        {totalHabits > 0 && (
          <View style={commonStyles.card}>
            <Text style={commonStyles.title}>Today's Progress</Text>
            <View style={commonStyles.row}>
              <Text style={commonStyles.subtitle}>
                {completedHabits} of {totalHabits} habits completed
              </Text>
              <View style={[
                commonStyles.badge,
                completionPercentage === 100 ? commonStyles.successBadge : 
                completionPercentage >= 50 ? commonStyles.warningBadge : null
              ]}>
                <Text style={commonStyles.badgeText}>{completionPercentage}%</Text>
              </View>
            </View>
          </View>
        )}

        {/* Add Habit Button */}
        <TouchableOpacity
          style={commonStyles.button}
          onPress={handleAddHabit}
        >
          <Text style={commonStyles.buttonText}>+ Add New Habit</Text>
        </TouchableOpacity>

        {/* Habits List */}
        {loading ? (
          <View style={commonStyles.centerContent}>
            <Text style={commonStyles.subtitle}>Loading habits...</Text>
          </View>
        ) : habits.length === 0 ? (
          <View style={commonStyles.emptyState}>
            <Text style={{ fontSize: 48, marginBottom: 16 }}>🎯</Text>
            <Text style={commonStyles.emptyStateText}>
              No habits yet! Start building positive habits by adding your first one.
            </Text>
          </View>
        ) : (
          <FlatList
            data={habits}
            renderItem={renderHabitItem}
            keyExtractor={(item) => item.id}
            style={{ flex: 1, marginTop: 16 }}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default MyHabits;
