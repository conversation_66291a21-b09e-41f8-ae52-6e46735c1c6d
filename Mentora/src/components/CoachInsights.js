import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { commonStyles, colors } from '../styles/styles';
import {
  getDailyCheckins,
  getHabits,
  getHabitCompletions,
} from '../utils/storage';

const { width } = Dimensions.get('window');

const CoachInsights = () => {
  const [insights, setInsights] = useState({
    moodTrend: null,
    energyTrend: null,
    habitStreak: 0,
    totalHabits: 0,
    weeklyCompletion: 0,
    motivationalMessage: '',
  });
  const [loading, setLoading] = useState(true);

  useFocusEffect(
    useCallback(() => {
      loadInsights();
    }, [])
  );

  const loadInsights = async () => {
    try {
      setLoading(true);
      
      const [checkins, habits, completions] = await Promise.all([
        getDailyCheckins(),
        getHabits(),
        getHabitCompletions(),
      ]);

      const calculatedInsights = calculateInsights(checkins, habits, completions);
      setInsights(calculatedInsights);
    } catch (error) {
      console.error('Error loading insights:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateInsights = (checkins, habits, completions) => {
    const now = new Date();
    const last7Days = [];
    
    // Get last 7 days
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      last7Days.push(date.toDateString());
    }

    // Calculate mood and energy trends
    const recentCheckins = last7Days
      .map(date => checkins[date])
      .filter(Boolean);

    let moodTrend = null;
    let energyTrend = null;

    if (recentCheckins.length >= 2) {
      const firstHalf = recentCheckins.slice(0, Math.ceil(recentCheckins.length / 2));
      const secondHalf = recentCheckins.slice(Math.floor(recentCheckins.length / 2));

      const avgMoodFirst = firstHalf.reduce((sum, c) => sum + c.mood, 0) / firstHalf.length;
      const avgMoodSecond = secondHalf.reduce((sum, c) => sum + c.mood, 0) / secondHalf.length;
      
      const avgEnergyFirst = firstHalf.reduce((sum, c) => sum + c.energy, 0) / firstHalf.length;
      const avgEnergySecond = secondHalf.reduce((sum, c) => sum + c.energy, 0) / secondHalf.length;

      moodTrend = avgMoodSecond > avgMoodFirst ? 'improving' : 
                  avgMoodSecond < avgMoodFirst ? 'declining' : 'stable';
      
      energyTrend = avgEnergySecond > avgEnergyFirst ? 'improving' : 
                    avgEnergySecond < avgEnergyFirst ? 'declining' : 'stable';
    }

    // Calculate habit completion stats
    const totalHabits = habits.length;
    let weeklyCompletion = 0;
    let currentStreak = 0;

    if (totalHabits > 0) {
      const completedDays = last7Days.filter(date => {
        const dayCompletions = completions[date] || [];
        const completionRate = dayCompletions.length / totalHabits;
        return completionRate >= 0.5; // At least 50% of habits completed
      });
      
      weeklyCompletion = Math.round((completedDays.length / 7) * 100);

      // Calculate current streak
      let streakDate = new Date(now);
      while (true) {
        const dateStr = streakDate.toDateString();
        const dayCompletions = completions[dateStr] || [];
        const completionRate = dayCompletions.length / totalHabits;
        
        if (completionRate >= 0.5) {
          currentStreak++;
          streakDate.setDate(streakDate.getDate() - 1);
        } else {
          break;
        }
      }
    }

    // Generate motivational message
    const motivationalMessage = generateMotivationalMessage({
      moodTrend,
      energyTrend,
      habitStreak: currentStreak,
      weeklyCompletion,
      totalHabits,
    });

    return {
      moodTrend,
      energyTrend,
      habitStreak: currentStreak,
      totalHabits,
      weeklyCompletion,
      motivationalMessage,
    };
  };

  const generateMotivationalMessage = ({ moodTrend, energyTrend, habitStreak, weeklyCompletion, totalHabits }) => {
    if (totalHabits === 0) {
      return "Ready to start your journey? Add your first habit and begin building positive changes! 🌱";
    }

    if (habitStreak >= 7) {
      return `Amazing! You're on a ${habitStreak}-day streak! Your consistency is paying off. Keep up the fantastic work! 🔥`;
    }

    if (weeklyCompletion >= 80) {
      return "You're crushing it this week! Your dedication to your habits is truly inspiring. 💪";
    }

    if (moodTrend === 'improving' && energyTrend === 'improving') {
      return "Your mood and energy are both trending upward! You're making great progress. 📈✨";
    }

    if (moodTrend === 'improving') {
      return "Your mood has been improving lately! Keep doing what you're doing. 😊";
    }

    if (energyTrend === 'improving') {
      return "Your energy levels are on the rise! You're building momentum. ⚡";
    }

    if (weeklyCompletion >= 50) {
      return "You're making steady progress! Every small step counts toward your bigger goals. 🎯";
    }

    if (habitStreak >= 3) {
      return `Great job maintaining your ${habitStreak}-day streak! Consistency is the key to lasting change. 🌟`;
    }

    return "Every day is a new opportunity to grow. You've got this! Start small and build momentum. 🚀";
  };

  const getTrendEmoji = (trend) => {
    switch (trend) {
      case 'improving': return '📈';
      case 'declining': return '📉';
      case 'stable': return '➡️';
      default: return '❓';
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'improving': return colors.success;
      case 'declining': return colors.error;
      case 'stable': return colors.warning;
      default: return colors.textSecondary;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.safeArea}>
        <View style={commonStyles.header}>
          <Text style={commonStyles.headerTitle}>Coach Insights</Text>
        </View>
        <View style={commonStyles.centerContent}>
          <Text style={commonStyles.subtitle}>Loading insights...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <View style={commonStyles.header}>
        <Text style={commonStyles.headerTitle}>Coach Insights</Text>
      </View>
      
      <ScrollView style={commonStyles.content}>
        {/* Motivational Message */}
        <View style={[commonStyles.card, { backgroundColor: colors.primary + '10' }]}>
          <Text style={{ fontSize: 24, marginBottom: 8 }}>🎯</Text>
          <Text style={[commonStyles.title, { color: colors.primary }]}>
            Your Coach Says:
          </Text>
          <Text style={[commonStyles.subtitle, { color: colors.text }]}>
            {insights.motivationalMessage}
          </Text>
        </View>

        {/* Habit Stats */}
        <View style={commonStyles.card}>
          <Text style={commonStyles.title}>Habit Progress</Text>
          
          <View style={commonStyles.row}>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text style={{ fontSize: 32, fontWeight: 'bold', color: colors.primary }}>
                {insights.habitStreak}
              </Text>
              <Text style={commonStyles.subtitle}>Day Streak</Text>
            </View>
            
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text style={{ fontSize: 32, fontWeight: 'bold', color: colors.success }}>
                {insights.weeklyCompletion}%
              </Text>
              <Text style={commonStyles.subtitle}>Weekly Completion</Text>
            </View>
          </View>
        </View>

        {/* Mood & Energy Trends */}
        {(insights.moodTrend || insights.energyTrend) && (
          <View style={commonStyles.card}>
            <Text style={commonStyles.title}>Recent Trends</Text>
            
            {insights.moodTrend && (
              <View style={[commonStyles.row, { marginBottom: 12 }]}>
                <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                  <Text style={{ fontSize: 20, marginRight: 8 }}>😊</Text>
                  <Text style={commonStyles.subtitle}>Mood</Text>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={{ fontSize: 20, marginRight: 8 }}>
                    {getTrendEmoji(insights.moodTrend)}
                  </Text>
                  <Text style={[
                    commonStyles.subtitle,
                    { color: getTrendColor(insights.moodTrend), fontWeight: '600' }
                  ]}>
                    {insights.moodTrend}
                  </Text>
                </View>
              </View>
            )}
            
            {insights.energyTrend && (
              <View style={commonStyles.row}>
                <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                  <Text style={{ fontSize: 20, marginRight: 8 }}>⚡</Text>
                  <Text style={commonStyles.subtitle}>Energy</Text>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={{ fontSize: 20, marginRight: 8 }}>
                    {getTrendEmoji(insights.energyTrend)}
                  </Text>
                  <Text style={[
                    commonStyles.subtitle,
                    { color: getTrendColor(insights.energyTrend), fontWeight: '600' }
                  ]}>
                    {insights.energyTrend}
                  </Text>
                </View>
              </View>
            )}
          </View>
        )}

        {/* Quick Tips */}
        <View style={commonStyles.card}>
          <Text style={commonStyles.title}>💡 Quick Tips</Text>
          <Text style={commonStyles.subtitle}>
            • Check in daily to track your mood and energy patterns
          </Text>
          <Text style={commonStyles.subtitle}>
            • Start with 2-3 small habits rather than many big ones
          </Text>
          <Text style={commonStyles.subtitle}>
            • Celebrate small wins - they add up to big changes!
          </Text>
          <Text style={commonStyles.subtitle}>
            • Be consistent rather than perfect
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default CoachInsights;
