var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=createIconSetFromFontello;var _createIconSet=_interopRequireDefault(require("./create-icon-set"));function createIconSetFromFontello(config,fontFamilyArg,fontFile){var glyphMap={};config.glyphs.forEach(function(glyph){glyphMap[glyph.css]=glyph.code;});var fontFamily=fontFamilyArg||config.name||'fontello';return(0,_createIconSet.default)(glyphMap,fontFamily,fontFile||fontFamily+".ttf");}