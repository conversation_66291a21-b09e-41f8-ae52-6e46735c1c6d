<?xml version="1.0" encoding="utf-8"?>
<!--Android Nougat exit animation-->
<!--http://aosp.opersys.com/xref/android-7.1.2_r37/xref/frameworks/base/core/res/res/anim/activity_close_exit.xml-->
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:shareInterpolator="false">
    <alpha android:fromAlpha="1.0" android:toAlpha="0.0"
        android:interpolator="@android:interpolator/linear"
        android:startOffset="100"
        android:duration="150"/>
    <translate android:fromYDelta="0%" android:toYDelta="8%"
        android:interpolator="@android:interpolator/accelerate_quint"
        android:duration="250"/>  <!--we use rns_no_animation_250.xml as the other animation for
        this transition since we want both of them to end at the same time. Remember to change
        duration in both files when modifying it-->
</set>
