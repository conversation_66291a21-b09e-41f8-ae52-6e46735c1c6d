{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_ScreenFooterNativeComponent", "e", "__esModule", "default", "ScreenFooter", "props", "createElement", "FooterComponent", "children", "collapsable", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/ScreenFooter.tsx"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,4BAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAgF,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhF;AACA;AACA;AACA,SAASG,YAAYA,CAACC,KAAgB,EAAE;EACtC,oBAAOR,MAAA,CAAAM,OAAA,CAAAG,aAAA,CAACN,4BAAA,CAAAG,OAA2B,EAAKE,KAAQ,CAAC;AACnD;AAMO,SAASE,eAAeA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EACzD,oBAAOX,MAAA,CAAAM,OAAA,CAAAG,aAAA,CAACF,YAAY;IAACK,WAAW,EAAE;EAAM,GAAED,QAAuB,CAAC;AACpE;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAR,OAAA,GAEcC,YAAY", "ignoreList": []}