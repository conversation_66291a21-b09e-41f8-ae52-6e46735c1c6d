"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RNSScreensRefContext = exports.GHContext = void 0;
var _react = _interopRequireDefault(require("react"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const GHContext = exports.GHContext = /*#__PURE__*/_react.default.createContext(props => /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, props.children));
const RNSScreensRefContext = exports.RNSScreensRefContext = /*#__PURE__*/_react.default.createContext(null);
//# sourceMappingURL=contexts.js.map