{"version": 3, "names": ["_reactNative", "require", "isSearchBarAvailableForCurrentPlatform", "exports", "includes", "Platform", "OS", "executeNativeBackPress", "<PERSON><PERSON><PERSON><PERSON>", "exitApp", "compatibilityFlags", "isNewBackTitleImplementation", "usesHeaderFlexboxImplementation"], "sourceRoot": "../../src", "sources": ["utils.ts"], "mappings": ";;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,sCAAsC,GAAAC,OAAA,CAAAD,sCAAA,GAAG,CACpD,KAAK,EACL,SAAS,CACV,CAACE,QAAQ,CAACC,qBAAQ,CAACC,EAAE,CAAC;AAEhB,SAASC,sBAAsBA,CAAA,EAAG;EACvC;EACAC,wBAAW,CAACC,OAAO,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,kBAAkB,GAAAP,OAAA,CAAAO,kBAAA,GAAG;EAChC;AACF;AACA;AACA;AACA;AACA;EACEC,4BAA4B,EAAE,IAAI;EAElC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,+BAA+B,EAAE;AACnC,CAAC", "ignoreList": []}