{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_reactNativeSafeAreaContext", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "width", "height", "Dimensions", "initialMetrics", "Platform", "OS", "initialWindowMetrics", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "children", "style", "createElement", "SafeAreaInsetsContext", "Consumer", "View", "styles", "container", "SafeAreaProvider", "StyleSheet", "create", "flex"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/SafeAreaProviderCompat.tsx"], "mappings": ";;;;;;AAEA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAQA,IAAAE,2BAAA,GAAAF,OAAA;AAIwC,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAfxC;AACA;;AAqBA,MAAM;EAAEW,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAGC,uBAAU,CAACd,GAAG,CAAC,QAAQ,CAAC;;AAE1D;AACA;AACA;AACA,MAAMe,cAAc,GAClBC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIC,gDAAoB,IAAI,IAAI,GACjD;EACEC,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAET,KAAK;IAAEC;EAAO,CAAC;EACpCS,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDR,gDAAoB;AAEX,SAASS,sBAAsBA,CAAC;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EACzE,oBACE1C,KAAA,CAAA2C,aAAA,CAACvC,2BAAA,CAAAwC,qBAAqB,CAACC,QAAQ,QAC5BV,MAAM,IAAI;IACT,IAAIA,MAAM,EAAE;MACV;MACA;MACA;MACA,oBAAOnC,KAAA,CAAA2C,aAAA,CAACxC,YAAA,CAAA2C,IAAI;QAACJ,KAAK,EAAE,CAACK,MAAM,CAACC,SAAS,EAAEN,KAAK;MAAE,GAAED,QAAe,CAAC;IAClE;IAEA,oBACEzC,KAAA,CAAA2C,aAAA,CAACvC,2BAAA,CAAA6C,gBAAgB;MAACrB,cAAc,EAAEA,cAAe;MAACc,KAAK,EAAEA;IAAM,GAC5DD,QACe,CAAC;EAEvB,CAC8B,CAAC;AAErC;AAEAD,sBAAsB,CAACZ,cAAc,GAAGA,cAAc;AAEtD,MAAMmB,MAAM,GAAGG,uBAAU,CAACC,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}