{"version": 3, "names": ["_reactNative", "require", "formSheetModalHeight", "getDefaultHeaderHeight", "layout", "statusBarHeight", "stackPresentation", "is<PERSON>arge<PERSON><PERSON>er", "headerHeight", "Platform", "OS", "isLandscape", "width", "height", "isFormSheetModal", "isPad", "isTV"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/getDefaultHeaderHeight.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAIA,MAAMC,oBAAoB,GAAG,EAAE;AAEhB,SAASC,sBAAsBA,CAC5CC,MAAc,EACdC,eAAuB,EACvBC,iBAAyC,EACzCC,aAAa,GAAG,KAAK,EACb;EACR;EACA,IAAIC,YAAY,GAAGC,qBAAQ,CAACC,EAAE,KAAK,SAAS,GAAG,EAAE,GAAG,EAAE;EAEtD,IAAID,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,MAAMC,WAAW,GAAGP,MAAM,CAACQ,KAAK,GAAGR,MAAM,CAACS,MAAM;IAChD,MAAMC,gBAAgB,GACpBR,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,WAAW,IACjCA,iBAAiB,KAAK,WAAW;IACnC,IAAIQ,gBAAgB,IAAI,CAACH,WAAW,EAAE;MACpC;MACAN,eAAe,GAAG,CAAC;IACrB;IAEA,IAAII,qBAAQ,CAACM,KAAK,IAAIN,qBAAQ,CAACO,IAAI,EAAE;MACnCR,YAAY,GAAGM,gBAAgB,GAAGZ,oBAAoB,GAAG,EAAE;IAC7D,CAAC,MAAM;MACL,IAAIS,WAAW,EAAE;QACfH,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACL,IAAIM,gBAAgB,EAAE;UACpBN,YAAY,GAAGN,oBAAoB;QACrC,CAAC,MAAM;UACLM,YAAY,GAAGD,aAAa,GAAG,EAAE,GAAG,EAAE;QACxC;MACF;IACF;EACF;EAEA,OAAOC,YAAY,GAAGH,eAAe;AACvC", "ignoreList": []}