"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = _interopRequireDefault(require("react"));
var _Screen = require("../components/Screen");
var _reactNativeReanimated = _interopRequireDefault(require("react-native-reanimated"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); } // @ts-ignore file to be used only if `react-native-reanimated` available in the project
const AnimatedScreen = _reactNativeReanimated.default.createAnimatedComponent(_Screen.InnerScreen);
const ReanimatedScreen = /*#__PURE__*/_react.default.forwardRef((props, ref) => {
  return /*#__PURE__*/_react.default.createElement(AnimatedScreen
  // @ts-ignore some problems with ref and onTransitionProgressReanimated being "fake" prop for parsing of `useEvent` return value
  , _extends({
    ref: ref
  }, props));
});
ReanimatedScreen.displayName = 'ReanimatedScreen';
var _default = exports.default = ReanimatedScreen;
//# sourceMappingURL=ReanimatedScreen.js.map