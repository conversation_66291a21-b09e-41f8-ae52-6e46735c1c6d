{"version": 3, "names": ["createNavigatorFactory", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "NativeStackView", "NativeStackNavigator", "initialRouteName", "children", "screenOptions", "rest", "state", "descriptors", "navigation", "useEffect", "dangerouslyGetParent", "undefined", "console", "warn", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "popToTop", "target", "key", "createElement", "_extends"], "sourceRoot": "../../../../src", "sources": ["native-stack/navigators/createNativeStackNavigator.tsx"], "mappings": ";AAAA,SACEA,sBAAsB,EAEtBC,YAAY,EAGZC,WAAW,EAGXC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAM9B,OAAOC,eAAe,MAAM,0BAA0B;AAEtD,SAASC,oBAAoBA,CAAC;EAC5BC,gBAAgB;EAChBC,QAAQ;EACRC,aAAa;EACb,GAAGC;AACsB,CAAC,EAAE;EAC5B,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAW,CAAC,GAAGV,oBAAoB,CAM7DD,WAAW,EAAE;IACbK,gBAAgB;IAChBC,QAAQ;IACRC;EACF,CAAC,CAAC;;EAEF;EACA;EACAL,KAAK,CAACU,SAAS,CAAC,MAAM;IACpB;IACA,IAAID,UAAU,EAAEE,oBAAoB,KAAKC,SAAS,EAAE;MAClDC,OAAO,CAACC,IAAI,CACV,2LACF,CAAC;IACH;EACF,CAAC,EAAE,CAACL,UAAU,CAAC,CAAC;EAEhBT,KAAK,CAACU,SAAS,CACb;EACE;EACCD,UAAU,EAA+CM,WAAW,GACnE,UAAU,EACTC,CAAM,IAAK;IACV,MAAMC,SAAS,GAAGR,UAAU,CAACQ,SAAS,CAAC,CAAC;;IAExC;IACA;IACAC,qBAAqB,CAAC,MAAM;MAC1B,IACEX,KAAK,CAACY,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAAgCI,gBAAgB,EACnD;QACA;QACA;QACAX,UAAU,CAACY,QAAQ,CAAC;UAClB,GAAGxB,YAAY,CAACyB,QAAQ,CAAC,CAAC;UAC1BC,MAAM,EAAEhB,KAAK,CAACiB;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CACF,CAAC,EACH,CAACf,UAAU,EAAEF,KAAK,CAACY,KAAK,EAAEZ,KAAK,CAACiB,GAAG,CACrC,CAAC;EAED,oBACExB,KAAA,CAAAyB,aAAA,CAACxB,eAAe,EAAAyB,QAAA,KACVpB,IAAI;IACRC,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA;EAAY,EAC1B,CAAC;AAEN;;AAEA;AACA;AACA;AACA,eAAeZ,sBAAsB,CAKnCM,oBAAoB,CAAC", "ignoreList": []}