{"version": 3, "names": ["React", "ScreenFooter", "FooterComponent", "children", "createElement", "collapsable"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/FooterComponent.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,+BAA+B;AAMxD,eAAe,SAASC,eAAeA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EACjE,oBAAOH,KAAA,CAAAI,aAAA,CAACH,YAAY;IAACI,WAAW,EAAE;EAAM,GAAEF,QAAuB,CAAC;AACpE", "ignoreList": []}