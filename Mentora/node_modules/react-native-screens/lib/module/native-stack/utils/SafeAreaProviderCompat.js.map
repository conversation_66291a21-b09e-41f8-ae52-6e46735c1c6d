{"version": 3, "names": ["React", "Dimensions", "Platform", "StyleSheet", "View", "initialWindowMetrics", "SafeAreaInsetsContext", "SafeAreaProvider", "width", "height", "get", "initialMetrics", "OS", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "children", "style", "createElement", "Consumer", "styles", "container", "create", "flex"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/SafeAreaProviderCompat.tsx"], "mappings": "AAAA;AACA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,UAAU,EACVC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,SACEC,oBAAoB,EACpBC,qBAAqB,EACrBC,gBAAgB,QACX,gCAAgC;AAOvC,MAAM;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAGR,UAAU,CAACS,GAAG,CAAC,QAAQ,CAAC;;AAE1D;AACA;AACA;AACA,MAAMC,cAAc,GAClBT,QAAQ,CAACU,EAAE,KAAK,KAAK,IAAIP,oBAAoB,IAAI,IAAI,GACjD;EACEQ,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEP,KAAK;IAAEC;EAAO,CAAC;EACpCO,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDf,oBAAoB;AAE1B,eAAe,SAASgB,sBAAsBA,CAAC;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EACzE,oBACEvB,KAAA,CAAAwB,aAAA,CAAClB,qBAAqB,CAACmB,QAAQ,QAC5BT,MAAM,IAAI;IACT,IAAIA,MAAM,EAAE;MACV;MACA;MACA;MACA,oBAAOhB,KAAA,CAAAwB,aAAA,CAACpB,IAAI;QAACmB,KAAK,EAAE,CAACG,MAAM,CAACC,SAAS,EAAEJ,KAAK;MAAE,GAAED,QAAe,CAAC;IAClE;IAEA,oBACEtB,KAAA,CAAAwB,aAAA,CAACjB,gBAAgB;MAACI,cAAc,EAAEA,cAAe;MAACY,KAAK,EAAEA;IAAM,GAC5DD,QACe,CAAC;EAEvB,CAC8B,CAAC;AAErC;AAEAD,sBAAsB,CAACV,cAAc,GAAGA,cAAc;AAEtD,MAAMe,MAAM,GAAGvB,UAAU,CAACyB,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}