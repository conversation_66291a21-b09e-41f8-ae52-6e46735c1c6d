{"version": 3, "names": ["Image", "View", "React", "ScreenStackHeaderBackButtonImage", "props", "createElement", "_extends", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.web.tsx"], "mappings": ";AAAA,SAASA,KAAK,EAAcC,IAAI,QAAmB,cAAc;AACjE,OAAOC,KAAK,MAAM,OAAO;AAGzB,OAAO,MAAMC,gCAAgC,GAC3CC,KAAiB,iBAEjBF,KAAA,CAAAG,aAAA,CAACJ,IAAI,qBACHC,KAAA,CAAAG,aAAA,CAACL,KAAK,EAAAM,QAAA;EAACC,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKJ,KAAK,CAAG,CACpD,CACP;AAED,OAAO,MAAMK,0BAA0B,GAAIL,KAAgB,iBACzDF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CACnB;AAED,OAAO,MAAMM,yBAAyB,GAAIN,KAAgB,iBACxDF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CACnB;AAED,OAAO,MAAMO,2BAA2B,GAAIP,KAAgB,iBAC1DF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CACnB;AAED,OAAO,MAAMQ,8BAA8B,GACzCR,KAAgB,iBACAF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CAAC;AAErC,OAAO,MAAMS,uBAAuB,GAClCT,KAAmC,iBACnBF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CAAC;AAErC,OAAO,MAAMU,wBAEZ,GAAGb,IAAI", "ignoreList": []}