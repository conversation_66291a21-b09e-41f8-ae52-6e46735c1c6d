{"version": 3, "names": ["React", "ScreenFooterNativeComponent", "ScreenFooter", "props", "createElement", "FooterComponent", "children", "collapsable"], "sourceRoot": "../../../src", "sources": ["components/ScreenFooter.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,2BAA2B,MAAM,uCAAuC;;AAE/E;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAgB,EAAE;EACtC,oBAAOH,KAAA,CAAAI,aAAA,CAACH,2BAA2B,EAAKE,KAAQ,CAAC;AACnD;AAMA,OAAO,SAASE,eAAeA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EACzD,oBAAON,KAAA,CAAAI,aAAA,CAACF,YAAY;IAACK,WAAW,EAAE;EAAM,GAAED,QAAuB,CAAC;AACpE;AAEA,eAAeJ,YAAY", "ignoreList": []}