{"version": 3, "names": ["React", "Platform", "InnerScreen", "Animated", "useEvent", "useSharedValue", "ReanimatedTransitionProgressContext", "useSafeAreaFrame", "useSafeAreaInsets", "getDefaultHeaderHeight", "getStatusBarHeight", "ReanimatedHeaderHeightContext", "AnimatedScreen", "createAnimatedComponent", "ENABLE_FABRIC", "global", "RN$Bridgeless", "ReanimatedNativeStackScreen", "forwardRef", "props", "ref", "children", "rest", "stackPresentation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimensions", "topInset", "top", "isStatusBarTranslucent", "statusBarTranslucent", "statusBarHeight", "defaultHeaderHeight", "cachedHeaderHeight", "useRef", "headerHeight", "progress", "closing", "goingForward", "createElement", "_extends", "onTransitionProgressReanimated", "event", "value", "OS", "onHeaderHeightChangeReanimated", "current", "Provider", "displayName"], "sourceRoot": "../../../src", "sources": ["reanimated/ReanimatedNativeStackScreen.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,WAAW,QAAQ,sBAAsB;AAOlD;AACA,OAAOC,QAAQ,IAAIC,QAAQ,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,OAAOC,mCAAmC,MAAM,uCAAuC;AACvF,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,gCAAgC;AACvC,OAAOC,sBAAsB,MAAM,8CAA8C;AACjF,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,6BAA6B,MAAM,iCAAiC;AAE3E,MAAMC,cAAc,GAAGT,QAAQ,CAACU,uBAAuB,CACrDX,WACF,CAAC;;AAED;AACA;AACA;AACA,MAAMY,aAAa,GAAG,CAAC,CAACC,MAAM,EAAEC,aAAa;AAE7C,MAAMC,2BAA2B,gBAAGjB,KAAK,CAACkB,UAAU,CAGlD,CAACC,KAAK,EAAEC,GAAG,KAAK;EAChB,MAAM;IAAEC,QAAQ;IAAE,GAAGC;EAAK,CAAC,GAAGH,KAAK;EACnC,MAAM;IAAEI,iBAAiB,GAAG,MAAM;IAAEC;EAAe,CAAC,GAAGF,IAAI;EAE3D,MAAMG,UAAU,GAAGlB,gBAAgB,CAAC,CAAC;EACrC,MAAMmB,QAAQ,GAAGlB,iBAAiB,CAAC,CAAC,CAACmB,GAAG;EACxC,MAAMC,sBAAsB,GAAGN,IAAI,CAACO,oBAAoB,IAAI,KAAK;EACjE,MAAMC,eAAe,GAAGpB,kBAAkB,CACxCgB,QAAQ,EACRD,UAAU,EACVG,sBACF,CAAC;;EAED;EACA;EACA,MAAMG,mBAAmB,GAAGtB,sBAAsB,CAChDgB,UAAU,EACVK,eAAe,EACfP,iBAAiB,EACjBC,cACF,CAAC;EAED,MAAMQ,kBAAkB,GAAGhC,KAAK,CAACiC,MAAM,CAACF,mBAAmB,CAAC;EAC5D,MAAMG,YAAY,GAAG7B,cAAc,CAAC0B,mBAAmB,CAAC;EAExD,MAAMI,QAAQ,GAAG9B,cAAc,CAAC,CAAC,CAAC;EAClC,MAAM+B,OAAO,GAAG/B,cAAc,CAAC,CAAC,CAAC;EACjC,MAAMgC,YAAY,GAAGhC,cAAc,CAAC,CAAC,CAAC;EAEtC,oBACEL,KAAA,CAAAsC,aAAA,CAAC1B;EACC;EAAA,EAAA2B,QAAA;IACAnB,GAAG,EAAEA,GAAI;IACToB,8BAA8B,EAAEpC,QAAQ,CACrCqC,KAAkC,IAAK;MACtC,SAAS;;MACTN,QAAQ,CAACO,KAAK,GAAGD,KAAK,CAACN,QAAQ;MAC/BC,OAAO,CAACM,KAAK,GAAGD,KAAK,CAACL,OAAO;MAC7BC,YAAY,CAACK,KAAK,GAAGD,KAAK,CAACJ,YAAY;IACzC,CAAC,EACD;IACE;IACA;IACApC,QAAQ,CAAC0C,EAAE,KAAK,SAAS,GACrB,sBAAsB;IACtB;IACF7B,aAAa,GACX,sBAAsB,GACtB,uBAAuB,CAE/B,CAAE;IACF8B,8BAA8B,EAAExC,QAAQ,CACrCqC,KAAkC,IAAK;MACtC,SAAS;;MACT,IAAIA,KAAK,CAACP,YAAY,KAAKF,kBAAkB,CAACa,OAAO,EAAE;QACrDX,YAAY,CAACQ,KAAK,GAAGD,KAAK,CAACP,YAAY;QACvCF,kBAAkB,CAACa,OAAO,GAAGJ,KAAK,CAACP,YAAY;MACjD;IACF,CAAC,EACD;IACE;IACAjC,QAAQ,CAAC0C,EAAE,KAAK,SAAS,GACrB,sBAAsB,GACtB7B,aAAa,GACb,sBAAsB,GACtB,uBAAuB,CAE/B;EAAE,GACEQ,IAAI,gBACRtB,KAAA,CAAAsC,aAAA,CAAC3B,6BAA6B,CAACmC,QAAQ;IAACJ,KAAK,EAAER;EAAa,gBAC1DlC,KAAA,CAAAsC,aAAA,CAAChC,mCAAmC,CAACwC,QAAQ;IAC3CJ,KAAK,EAAE;MACLP,QAAQ;MACRC,OAAO;MACPC;IACF;EAAE,GACDhB,QAC2C,CACR,CAC1B,CAAC;AAErB,CAAC,CAAC;AAEFJ,2BAA2B,CAAC8B,WAAW,GAAG,6BAA6B;AAEvE,eAAe9B,2BAA2B", "ignoreList": []}