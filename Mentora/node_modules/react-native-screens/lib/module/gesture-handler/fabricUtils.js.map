{"version": 3, "names": ["isF<PERSON><PERSON>", "global", "RN$Bridgeless", "findHostInstance_DEPRECATED", "getInternalInstanceHandleFromPublicInstance", "getShadowNodeWrapperAndTagFromRef", "ref", "undefined", "require", "e", "_ref", "_internalInstanceHandle", "scrollViewRef", "getScrollResponder", "getNativeScrollRef", "otherScrollViewRef", "textInputRef", "__internalInstanceHandle", "stateNode", "node", "resolvedRef", "shadowNodeWrapper", "tag", "_nativeTag", "__nativeTag", "hostInstance"], "sourceRoot": "../../../src", "sources": ["gesture-handler/fabricUtils.ts"], "mappings": "AAAA,YAAY;;AAIZ;;AAIA,OAAO,SAASA,QAAQA,CAAA,EAAG;EACzB,OAAO,CAAC,CAAEC,MAAM,CAAiBC,aAAa;AAChD;AAMA,IAAIC,2BAAmD;AAEvD,IAAIC,2CAEH;;AAED;AACA,OAAO,SAASC,iCAAiCA,CAACC,GAAgB,EAGhE;EACA;EACA,IAAIH,2BAA2B,KAAKI,SAAS,EAAE;IAC7C,IAAI;MACFJ,2BAA2B,GACzBK,OAAO,CAAC,mDAAmD,CAAC,CAACL,2BAA2B;IAC5F,CAAC,CAAC,OAAOM,CAAC,EAAE;MACVN,2BAA2B,GAAIO,IAAa,IAAK,IAAI;IACvD;EACF;EAEA,IAAIN,2CAA2C,KAAKG,SAAS,EAAE;IAC7D,IAAI;MACFH,2CAA2C,GACzCI,OAAO,CAAC,wFAAwF,CAAC,CAC9FJ,2CAA2C,KAC5CM,IAAS,IAAKA,IAAI,CAACC,uBAAuB,CAAC;IACjD,CAAC,CAAC,OAAOF,CAAC,EAAE;MACVL,2CAA2C,GAAIM,IAAS,IACtDA,IAAI,CAACC,uBAAuB;IAChC;EACF;;EAEA;EACA;EACA,MAAMC,aAAa,GAAGN,GAAG,EAAEO,kBAAkB,GAAG,CAAC,EAAEC,kBAAkB,GAAG,CAAC;EACzE;EACA,MAAMC,kBAAkB,GAAGT,GAAG,EAAEQ,kBAAkB,GAAG,CAAC;EACtD;EACA,MAAME,YAAY,GAAGV,GAAG,EAAEW,wBAAwB,EAAEC,SAAS,EAAEC,IAAI;EAEnE,IAAIC,WAAW;EACf,IAAIR,aAAa,EAAE;IACjBQ,WAAW,GAAG;MACZC,iBAAiB,EAAET,aAAa,CAACK,wBAAwB,CAACC,SAAS,CAACC,IAAI;MACxEG,GAAG,EAAEV,aAAa,CAACW;IACrB,CAAC;EACH,CAAC,MAAM,IAAIR,kBAAkB,EAAE;IAC7BK,WAAW,GAAG;MACZC,iBAAiB,EACfN,kBAAkB,CAACE,wBAAwB,CAACC,SAAS,CAACC,IAAI;MAC5DG,GAAG,EAAEP,kBAAkB,CAACS;IAC1B,CAAC;EACH,CAAC,MAAM,IAAIR,YAAY,EAAE;IACvBI,WAAW,GAAG;MACZC,iBAAiB,EAAEL,YAAY;MAC/BM,GAAG,EAAGhB,GAAG,EAAUkB;IACrB,CAAC;EACH,CAAC,MAAM;IACL,MAAMC,YAAY,GAAGtB,2BAA2B,CAACG,GAAG,CAAC;IACrDc,WAAW,GAAG;MACZC,iBAAiB,EACfjB,2CAA2C,CAACqB,YAAY,CAAC,CAACP,SAAS,CAChEC,IAAI;MACTG,GAAG,EAAGG,YAAY,EAAUF;IAC9B,CAAC;EACH;EAEA,OAAOH,WAAW;AACpB", "ignoreList": []}