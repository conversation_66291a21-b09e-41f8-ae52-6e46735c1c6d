{"version": 3, "names": ["PointerType", "ScreenTransition", "DefaultEvent", "absoluteX", "absoluteY", "handlerTag", "numberOfPointers", "state", "translationX", "translationY", "velocityX", "velocityY", "x", "y", "pointerType", "TOUCH", "DefaultScreenDimensions", "width", "height", "pageX", "pageY", "AnimationForGesture", "swipeRight", "SwipeRight", "swipeLeft", "SwipeLeft", "swipeDown", "SwipeDown", "swipeUp", "SwipeUp", "horizontalSwipe", "Horizontal", "verticalSwipe", "Vertical", "twoDimensionalSwipe", "TwoDimensional"], "sourceRoot": "../../../src", "sources": ["gesture-handler/defaults.ts"], "mappings": "AAAA,SAGEA,WAAW,QACN,8BAA8B;AACrC,SAASC,gBAAgB,QAAQ,yBAAyB;AAE1D,OAAO,MAAMC,YAA+D,GAAG;EAC7EC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE,CAAC;EACbC,gBAAgB,EAAE,CAAC;EACnBC,KAAK,EAAE,CAAC;EACRC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC;EACZC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EAEJ;EACA;EACA;EACA;EACAC,WAAW,EAAEd,WAAW,CAACe;AAC3B,CAAC;AAED,OAAO,MAAMC,uBAAuB,GAAG;EACrCC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTN,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJM,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG;EACjCC,UAAU,EAAErB,gBAAgB,CAACsB,UAAU;EACvCC,SAAS,EAAEvB,gBAAgB,CAACwB,SAAS;EACrCC,SAAS,EAAEzB,gBAAgB,CAAC0B,SAAS;EACrCC,OAAO,EAAE3B,gBAAgB,CAAC4B,OAAO;EACjCC,eAAe,EAAE7B,gBAAgB,CAAC8B,UAAU;EAC5CC,aAAa,EAAE/B,gBAAgB,CAACgC,QAAQ;EACxCC,mBAAmB,EAAEjC,gBAAgB,CAACkC;AACxC,CAAC", "ignoreList": []}