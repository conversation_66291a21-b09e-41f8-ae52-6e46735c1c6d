/**
 * Mentora - Personal Growth & Habit Tracking App
 * @format
 */

import React from 'react';
import { StatusBar, Text } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Import components
import DailyCheckin from './src/components/DailyCheckin';
import MyHabits from './src/components/MyHabits';
import CoachInsights from './src/components/CoachInsights';

// Import styles
import { colors } from './src/styles/styles';

const Tab = createBottomTabNavigator();

// Simple icon component since we don't have vector icons set up yet
const TabIcon = ({ focused, emoji }: { focused: boolean; emoji: string }) => (
  <Text style={{
    fontSize: focused ? 24 : 20,
    opacity: focused ? 1 : 0.6
  }}>
    {emoji}
  </Text>
);

function App(): React.JSX.Element {
  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={colors.background}
      />
      <NavigationContainer>
        <Tab.Navigator
          screenOptions={{
            headerShown: false,
            tabBarActiveTintColor: colors.primary,
            tabBarInactiveTintColor: colors.textSecondary,
            tabBarStyle: {
              backgroundColor: colors.surface,
              borderTopColor: colors.border,
              paddingBottom: 8,
              paddingTop: 8,
              height: 80,
            },
            tabBarLabelStyle: {
              fontSize: 12,
              fontWeight: '600',
              marginTop: 4,
            },
          }}
        >
          <Tab.Screen
            name="DailyCheckin"
            component={DailyCheckin}
            options={{
              tabBarLabel: 'Daily Check-in',
              tabBarIcon: ({ focused }) => (
                <TabIcon focused={focused} emoji="📝" />
              ),
            }}
          />
          <Tab.Screen
            name="MyHabits"
            component={MyHabits}
            options={{
              tabBarLabel: 'My Habits',
              tabBarIcon: ({ focused }) => (
                <TabIcon focused={focused} emoji="🎯" />
              ),
            }}
          />
          <Tab.Screen
            name="CoachInsights"
            component={CoachInsights}
            options={{
              tabBarLabel: 'Coach Insights',
              tabBarIcon: ({ focused }) => (
                <TabIcon focused={focused} emoji="💡" />
              ),
            }}
          />
        </Tab.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}

export default App;
